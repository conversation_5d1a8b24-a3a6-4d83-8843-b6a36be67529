import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"

    # MCP配置
    MCP_SERVER_SCRIPT = "core/file_operations_mcp.py"

    # 文件操作配置
    DEFAULT_WORK_DIR = "./workspace"
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = ['.txt', '.py', '.json', '.md', '.yaml', '.yml', '.xml', '.csv']

    # 天气API配置
    WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"
    WEATHER_API_KEY = "d5c020123a565d7659190293f6c159ed"

    # 安全配置 - 限制文件操作范围
    SAFE_MODE = True  # 启用安全模式
    ALLOWED_BASE_DIR = Path(__file__).parent.resolve()  # 仅允许在ApplyMCP目录内操作

    # 禁止操作的目录和文件
    FORBIDDEN_DIRS = [
        "__pycache__",
        ".git",
        ".vscode",
        "node_modules"
    ]

    FORBIDDEN_FILES = [
        "config.py",  # 保护配置文件
        "file_operations_mcp.py",  # 保护MCP服务器
        "agent.py",  # 保护核心代理
        "llm_client.py",  # 保护LLM客户端
        "mcp_client.py"  # 保护MCP客户端
    ]

    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()

    @classmethod
    def is_path_safe(cls, file_path: str) -> tuple[bool, str]:
        """
        检查路径是否安全

        Args:
            file_path: 要检查的文件路径

        Returns:
            tuple: (是否安全, 错误信息)
        """
        if not cls.SAFE_MODE:
            return True, ""

        try:
            # 转换为绝对路径
            abs_path = Path(file_path).resolve()

            # 检查是否在允许的基础目录内
            if not str(abs_path).startswith(str(cls.ALLOWED_BASE_DIR)):
                return False, f"❌ 安全限制: 不允许访问ApplyMCP目录外的文件: {file_path}"

            # 检查是否是禁止的目录
            for forbidden_dir in cls.FORBIDDEN_DIRS:
                if forbidden_dir in abs_path.parts:
                    return False, f"❌ 安全限制: 不允许访问系统目录: {forbidden_dir}"

            # 检查是否是禁止的文件
            if abs_path.name in cls.FORBIDDEN_FILES:
                return False, f"❌ 安全限制: 不允许修改核心文件: {abs_path.name}"

            return True, ""

        except Exception as e:
            return False, f"❌ 路径验证错误: {str(e)}"
