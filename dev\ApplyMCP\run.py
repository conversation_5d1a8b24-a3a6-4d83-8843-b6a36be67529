#!/usr/bin/env python3
"""
启动脚本 - 提供多种运行选项
"""

import sys
import os
import asyncio
import argparse

def check_dependencies():
    """检查依赖包"""
    required_packages = ['openai', 'mcp']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_api_key():
    """检查API密钥"""
    api_file = os.path.join(os.path.dirname(__file__), "..", "api.txt")
    if not os.path.exists(api_file):
        print("❌ 未找到API密钥文件: ../api.txt")
        print("请确保在上级目录中创建api.txt文件并填入您的LLM API密钥")
        return False
    
    try:
        with open(api_file, 'r', encoding='utf-8') as f:
            key = f.read().strip()
            if not key:
                print("❌ API密钥文件为空")
                return False
        print("✅ API密钥配置正常")
        return True
    except Exception as e:
        print(f"❌ 读取API密钥失败: {str(e)}")
        return False

async def run_chat():
    """运行对话程序"""
    from main import main
    await main()

async def run_test():
    """运行测试程序"""
    from test_mcp import main
    await main()

def show_info():
    """显示项目信息"""
    print("📁 文件操作MCP对话工具")
    print("=" * 40)
    print("🔧 功能: 基于MCP的智能文件操作助手")
    print("🤖 支持: 自然语言文件操作对话")
    print("🛠️  架构: MCP + LLM + 异步处理")
    print("\n📋 可用命令:")
    print("  python run.py chat    # 启动对话程序")
    print("  python run.py test    # 运行功能测试")
    print("  python run.py check   # 检查环境配置")
    print("  python run.py info    # 显示项目信息")
    print("\n📖 更多信息请查看 README.md")

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    print("-" * 30)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    else:
        print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖包
    if not check_dependencies():
        return False
    else:
        print("✅ 依赖包检查通过")
    
    # 检查API密钥
    if not check_api_key():
        return False
    
    # 检查文件权限
    try:
        test_file = "temp_test.txt"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ 文件读写权限正常")
    except Exception as e:
        print(f"❌ 文件读写权限检查失败: {str(e)}")
        return False
    
    print("\n🎉 环境检查通过，可以正常运行！")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="文件操作MCP对话工具启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run.py chat     # 启动对话程序
  python run.py test     # 运行功能测试
  python run.py check    # 检查环境配置
  python run.py info     # 显示项目信息
        """
    )
    
    parser.add_argument(
        'command',
        choices=['chat', 'test', 'check', 'info'],
        help='要执行的命令'
    )
    
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    args = parser.parse_args()
    
    try:
        if args.command == 'info':
            show_info()
        elif args.command == 'check':
            check_environment()
        elif args.command == 'test':
            if check_environment():
                print("\n🧪 开始运行测试...")
                asyncio.run(run_test())
        elif args.command == 'chat':
            if check_environment():
                print("\n🚀 启动对话程序...")
                asyncio.run(run_chat())
    
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
