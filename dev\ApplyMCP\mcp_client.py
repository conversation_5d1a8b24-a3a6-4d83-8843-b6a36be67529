#!/usr/bin/env python3
"""
简化的MCP客户端
用于与文件操作MCP服务器通信
使用JSON-RPC协议
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict, List, Optional

class MCPClient:
    def __init__(self, server_script_path: str):
        """
        初始化MCP客户端

        Args:
            server_script_path: MCP服务器脚本路径
        """
        self.server_script_path = server_script_path
        self.server_process = None
        self.available_tools: List[Dict[str, Any]] = []
        self.request_id = 0

    async def connect(self):
        """连接到MCP服务器"""
        try:
            # 启动MCP服务器进程
            self.server_process = await asyncio.create_subprocess_exec(
                sys.executable, self.server_script_path,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 获取可用工具列表
            await self._load_tools()

            print(f"成功连接到MCP服务器，可用工具: {len(self.available_tools)}")
            return True

        except Exception as e:
            print(f"连接MCP服务器失败: {str(e)}")
            return False

    async def _send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送JSON-RPC请求"""
        if not self.server_process:
            raise Exception("未连接到MCP服务器")

        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }

        # 发送请求
        request_json = json.dumps(request) + "\n"
        self.server_process.stdin.write(request_json.encode())
        await self.server_process.stdin.drain()

        # 读取响应
        response_line = await self.server_process.stdout.readline()
        response_data = json.loads(response_line.decode().strip())

        if "error" in response_data:
            raise Exception(f"服务器错误: {response_data['error']['message']}")

        return response_data.get("result", {})

    async def _load_tools(self):
        """加载可用工具列表"""
        try:
            result = await self._send_request("list_tools")
            self.available_tools = result.get("tools", [])
        except Exception as e:
            print(f"加载工具列表失败: {str(e)}")
            self.available_tools = []
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return self.available_tools
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        if not self.server_process:
            raise Exception("未连接到MCP服务器")

        try:
            result = await self._send_request("call_tool", {
                "name": tool_name,
                "arguments": arguments
            })
            return result

        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"工具调用失败: {str(e)}"}]
            }
    
    async def read_file(self, file_path: str) -> str:
        """读取文件内容"""
        result = await self.call_tool("read_file", {"file_path": file_path})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"读取文件失败: {result}")
    
    async def write_file(self, file_path: str, content: str, mode: str = "write") -> bool:
        """写入文件内容"""
        result = await self.call_tool("write_file", {
            "file_path": file_path,
            "content": content,
            "mode": mode
        })
        return result["success"]
    
    async def create_directory(self, dir_path: str) -> bool:
        """创建目录"""
        result = await self.call_tool("create_directory", {"dir_path": dir_path})
        return result["success"]
    
    async def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        result = await self.call_tool("delete_file", {"file_path": file_path})
        return result["success"]
    
    async def list_directory(self, dir_path: str) -> str:
        """列出目录内容"""
        result = await self.call_tool("list_directory", {"dir_path": dir_path})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"列出目录失败: {result}")
    
    async def copy_file(self, source_path: str, dest_path: str) -> bool:
        """复制文件"""
        result = await self.call_tool("copy_file", {
            "source_path": source_path,
            "dest_path": dest_path
        })
        return result["success"]
    
    async def move_file(self, source_path: str, dest_path: str) -> bool:
        """移动文件"""
        result = await self.call_tool("move_file", {
            "source_path": source_path,
            "dest_path": dest_path
        })
        return result["success"]
    
    async def get_file_info(self, file_path: str) -> str:
        """获取文件信息"""
        result = await self.call_tool("get_file_info", {"file_path": file_path})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取文件信息失败: {result}")

    # 天气相关方法
    async def get_current_weather(self, city: str = "北京") -> str:
        """获取当前天气"""
        result = await self.call_tool("get_current_weather", {"city": city})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取天气信息失败: {result}")

    async def get_weather_forecast(self, city: str = "北京", days: int = 3) -> str:
        """获取天气预报"""
        result = await self.call_tool("get_weather_forecast", {"city": city, "days": days})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取天气预报失败: {result}")

    # 时间相关方法
    async def get_current_time(self, format_type: str = "detailed") -> str:
        """获取当前时间"""
        result = await self.call_tool("get_current_time", {"format_type": format_type})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取时间信息失败: {result}")

    async def get_time_info(self, info_type: str = "period") -> str:
        """获取时间相关信息"""
        result = await self.call_tool("get_time_info", {"info_type": info_type})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取时间信息失败: {result}")

    async def get_date_info(self, info_type: str = "basic") -> str:
        """获取日期信息"""
        result = await self.call_tool("get_date_info", {"info_type": info_type})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"获取日期信息失败: {result}")

    async def get_countdown(self, target_date: str) -> str:
        """获取倒计时"""
        result = await self.call_tool("get_countdown", {"target_date": target_date})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"计算倒计时失败: {result}")

    async def disconnect(self):
        """断开连接"""
        if self.server_process:
            try:
                self.server_process.terminate()
                await self.server_process.wait()
            except:
                pass
            self.server_process = None

# 使用示例
async def test_mcp_client():
    """测试MCP客户端"""
    client = MCPClient("file_operations_mcp.py")
    
    try:
        # 连接到服务器
        if not await client.connect():
            return
        
        # 测试创建目录
        await client.create_directory("test_dir")
        print("创建目录成功")
        
        # 测试写入文件
        await client.write_file("test_dir/test.txt", "Hello, MCP!")
        print("写入文件成功")
        
        # 测试读取文件
        content = await client.read_file("test_dir/test.txt")
        print(f"读取文件内容: {content}")
        
        # 测试列出目录
        dir_content = await client.list_directory("test_dir")
        print(f"目录内容: {dir_content}")
        
        # 测试获取文件信息
        file_info = await client.get_file_info("test_dir/test.txt")
        print(f"文件信息: {file_info}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_mcp_client())
