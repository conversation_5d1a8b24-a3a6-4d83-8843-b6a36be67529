#!/usr/bin/env python3
"""
测试重构后的项目结构
"""

def test_imports():
    """测试模块导入"""
    print("🧪 测试重构后的项目结构...")
    print("=" * 50)
    
    try:
        # 测试核心模块导入
        from core.agent import FileOperationAgent
        print("✅ 核心模块导入成功: FileOperationAgent")
        
        from core.file_operations_mcp import SimpleJSONRPCServer
        print("✅ 核心模块导入成功: SimpleJSONRPCServer")
        
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
    
    try:
        # 测试服务模块导入
        from services.weather_service import WeatherService
        print("✅ 服务模块导入成功: WeatherService")
        
        from services.time_service import TimeService
        print("✅ 服务模块导入成功: TimeService")
        
    except Exception as e:
        print(f"❌ 服务模块导入失败: {e}")
    
    try:
        # 测试客户端模块导入
        from clients.mcp_client import MCPClient
        print("✅ 客户端模块导入成功: MCPClient")
        
        from clients.llm_client import LLMClient
        print("✅ 客户端模块导入成功: LLMClient")
        
    except Exception as e:
        print(f"❌ 客户端模块导入失败: {e}")
    
    try:
        # 测试配置模块导入
        from config_dir.config import Config
        print("✅ 配置模块导入成功: Config")
        
        from config_dir.prompts import SYSTEM_PROMPT, FILE_OPERATION_PROMPTS
        print("✅ 配置模块导入成功: SYSTEM_PROMPT, FILE_OPERATION_PROMPTS")
        
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    print("-" * 30)
    
    try:
        # 测试时间服务
        from services.time_service import TimeService
        time_service = TimeService()
        current_time = time_service.get_current_time("detailed")
        print(f"✅ 时间服务正常: {current_time['datetime']}")
        
    except Exception as e:
        print(f"❌ 时间服务测试失败: {e}")
    
    try:
        # 测试天气服务初始化
        from services.weather_service import WeatherService
        from config_dir.config import Config
        weather_service = WeatherService(
            api_key=Config.WEATHER_API_KEY,
            base_url=Config.WEATHER_BASE_URL
        )
        print("✅ 天气服务初始化成功")
        
    except Exception as e:
        print(f"❌ 天气服务测试失败: {e}")
    
    try:
        # 测试配置
        from config_dir.config import Config
        print(f"✅ 配置加载成功: MCP服务器路径 = {Config.MCP_SERVER_SCRIPT}")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")

def show_project_structure():
    """显示项目结构"""
    print("\n📁 项目结构整理完成!")
    print("=" * 50)
    print("""
ApplyMCP/
├── 📁 core/          # 核心模块 (代理、MCP服务器)
├── 📁 services/      # 服务模块 (天气、时间服务)  
├── 📁 clients/       # 客户端模块 (MCP、LLM客户端)
├── 📁 config_dir/    # 配置模块 (配置、提示词)
├── 📁 tests/         # 测试模块
├── 📁 docs/          # 文档
├── 📁 examples/      # 示例演示
├── 📁 utils/         # 工具模块
├── 📁 workspace/     # 工作目录
├── main.py           # 主程序入口
├── requirements.txt  # 依赖包列表
└── README.md         # 项目说明
    """)
    
    print("🎯 整理成果:")
    print("✅ 模块化结构清晰")
    print("✅ 功能分类合理")
    print("✅ 导入路径更新")
    print("✅ 文档结构完善")
    print("✅ 包初始化文件完整")
    
    print("\n💡 使用建议:")
    print("- 运行 'python main.py' 启动主程序")
    print("- 查看 'PROJECT_STRUCTURE.md' 了解详细结构")
    print("- 查看 'README.md' 了解使用方法")

if __name__ == "__main__":
    test_imports()
    test_basic_functionality()
    show_project_structure()
    print("\n🎉 项目结构整理测试完成！")
