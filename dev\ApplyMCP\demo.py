#!/usr/bin/env python3
"""
文件操作MCP对话工具演示脚本
"""

import asyncio
from agent import FileOperationAgent

async def demo():
    """演示脚本"""
    print("🎬 文件操作MCP对话工具演示")
    print("=" * 50)
    
    # 创建代理
    agent = FileOperationAgent()
    
    # 初始化
    print("🔄 初始化系统...")
    await agent.initialize()
    
    if not agent.is_connected:
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化成功！")
    print("\n🎯 开始演示文件操作功能...")
    
    # 演示场景
    scenarios = [
        {
            "title": "📝 创建项目文档",
            "command": "创建一个名为 project_readme.md 的文件，内容是：# 我的项目\n\n这是一个示例项目。",
            "description": "演示创建Markdown文档"
        },
        {
            "title": "📖 查看文档内容",
            "command": "读取 project_readme.md 文件的内容",
            "description": "演示读取文件功能"
        },
        {
            "title": "📁 创建项目结构",
            "command": "创建一个名为 src 的目录",
            "description": "演示创建目录功能"
        },
        {
            "title": "📄 创建源代码文件",
            "command": "在 src 目录创建一个名为 main.py 的文件，内容是：print('Hello, MCP World!')",
            "description": "演示在子目录中创建文件"
        },
        {
            "title": "📋 查看项目结构",
            "command": "列出当前目录的所有文件和文件夹",
            "description": "演示列出目录内容"
        },
        {
            "title": "📄 备份重要文件",
            "command": "复制 project_readme.md 到 project_readme_backup.md",
            "description": "演示文件复制功能"
        },
        {
            "title": "✏️ 更新文档",
            "command": "在 project_readme.md 文件末尾追加：\n\n## 更新日志\n- 添加了主程序文件",
            "description": "演示追加文件内容"
        },
        {
            "title": "ℹ️ 查看文件信息",
            "command": "获取 project_readme.md 的详细信息",
            "description": "演示获取文件信息功能"
        },
        {
            "title": "🔄 重命名文件",
            "command": "将 src/main.py 移动到 src/app.py",
            "description": "演示文件重命名功能"
        },
        {
            "title": "📋 查看最终结构",
            "command": "列出 src 目录的内容",
            "description": "验证文件操作结果"
        }
    ]
    
    print(f"\n🎪 演示包含 {len(scenarios)} 个场景")
    print("=" * 50)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🎬 场景 {i}: {scenario['title']}")
        print(f"📝 说明: {scenario['description']}")
        print(f"💬 命令: {scenario['command']}")
        print("-" * 30)
        
        try:
            # 模拟用户输入
            print("🤔 AI助手思考中...")
            response = await agent.process_message(scenario['command'])
            print(f"🤖 AI助手: {response}")
            print("✅ 场景完成")
        except Exception as e:
            print(f"❌ 场景失败: {str(e)}")
        
        print("=" * 50)
        
        # 添加小延迟使演示更自然
        await asyncio.sleep(1)
    
    print("\n🧹 清理演示文件...")
    cleanup_commands = [
        "删除 project_readme.md",
        "删除 project_readme_backup.md", 
        "删除 src 目录"
    ]
    
    for cmd in cleanup_commands:
        try:
            await agent.process_message(cmd)
            print(f"✅ {cmd}")
        except:
            print(f"⚠️ {cmd} (可能已不存在)")
    
    # 清理
    await agent.cleanup()
    
    print("\n🎉 演示完成！")
    print("\n📊 演示总结:")
    print("✅ 文件创建和写入")
    print("✅ 文件读取和查看")
    print("✅ 目录创建和管理")
    print("✅ 文件复制和备份")
    print("✅ 内容追加和更新")
    print("✅ 文件信息查询")
    print("✅ 文件移动和重命名")
    print("✅ 目录结构查看")
    print("✅ 智能对话交互")
    
    print("\n🚀 项目特色:")
    print("🔧 基于MCP协议的文件操作")
    print("🤖 自然语言智能交互")
    print("📁 完整的文件管理功能")
    print("🛡️ 安全的操作验证")
    print("🎯 用户友好的界面")

if __name__ == "__main__":
    asyncio.run(demo())
