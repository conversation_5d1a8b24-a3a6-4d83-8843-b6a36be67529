#!/usr/bin/env python3
"""
文件操作对话代理
集成MCP文件操作功能的智能助手
"""

import asyncio
import json
import os
from typing import Dict, List, Any, Optional

from llm_client import LLMClient
from mcp_client import MCPClient
from config import Config
from prompts import FILE_OPERATION_PROMPTS

class FileOperationAgent:
    def __init__(self):
        """初始化文件操作代理"""
        self.llm_client = LLMClient()
        self.mcp_client = None
        self.conversation_history = []
        self.is_connected = False
        
    async def initialize(self):
        """初始化MCP连接"""
        try:
            # 获取MCP服务器脚本的绝对路径
            script_path = os.path.join(os.path.dirname(__file__), Config.MCP_SERVER_SCRIPT)
            self.mcp_client = MCPClient(script_path)
            
            # 连接到MCP服务器
            self.is_connected = await self.mcp_client.connect()
            
            if self.is_connected:
                print("✅ MCP文件操作服务已启动")
                # 创建默认工作目录
                await self._ensure_work_directory()
            else:
                print("❌ MCP服务启动失败")
                
        except Exception as e:
            print(f"❌ 初始化失败: {str(e)}")
            self.is_connected = False
    
    async def _ensure_work_directory(self):
        """确保工作目录存在"""
        try:
            if not os.path.exists(Config.DEFAULT_WORK_DIR):
                await self.mcp_client.create_directory(Config.DEFAULT_WORK_DIR)
                print(f"📁 创建工作目录: {Config.DEFAULT_WORK_DIR}")
        except Exception as e:
            print(f"⚠️ 创建工作目录失败: {str(e)}")
    
    def _get_file_operation_functions(self) -> List[Dict[str, Any]]:
        """获取文件操作函数定义"""
        if not self.is_connected:
            return []
        
        return [
            {
                "name": "read_file",
                "description": "读取文件内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要读取的文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "write_file",
                "description": "写入文件内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要写入的文件路径"
                        },
                        "content": {
                            "type": "string",
                            "description": "要写入的内容"
                        },
                        "mode": {
                            "type": "string",
                            "enum": ["write", "append"],
                            "description": "写入模式：write(覆盖)或append(追加)",
                            "default": "write"
                        }
                    },
                    "required": ["file_path", "content"]
                }
            },
            {
                "name": "create_directory",
                "description": "创建目录",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "dir_path": {
                            "type": "string",
                            "description": "要创建的目录路径"
                        }
                    },
                    "required": ["dir_path"]
                }
            },
            {
                "name": "delete_file",
                "description": "删除文件或目录",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要删除的文件或目录路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "list_directory",
                "description": "列出目录内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "dir_path": {
                            "type": "string",
                            "description": "要列出的目录路径"
                        }
                    },
                    "required": ["dir_path"]
                }
            },
            {
                "name": "copy_file",
                "description": "复制文件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "source_path": {
                            "type": "string",
                            "description": "源文件路径"
                        },
                        "dest_path": {
                            "type": "string",
                            "description": "目标文件路径"
                        }
                    },
                    "required": ["source_path", "dest_path"]
                }
            },
            {
                "name": "move_file",
                "description": "移动/重命名文件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "source_path": {
                            "type": "string",
                            "description": "源文件路径"
                        },
                        "dest_path": {
                            "type": "string",
                            "description": "目标文件路径"
                        }
                    },
                    "required": ["source_path", "dest_path"]
                }
            },
            {
                "name": "get_file_info",
                "description": "获取文件信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            }
        ]
    
    async def _execute_file_operation(self, function_name: str, arguments: Dict[str, Any]) -> str:
        """执行文件操作"""
        if not self.is_connected:
            return "❌ MCP服务未连接，无法执行文件操作"
        
        try:
            # 显示操作提示
            if function_name in FILE_OPERATION_PROMPTS:
                prompt = FILE_OPERATION_PROMPTS[function_name].format(**arguments)
                print(f"🔄 {prompt}")
            
            # 调用MCP工具
            result = await self.mcp_client.call_tool(function_name, arguments)
            
            if result["success"]:
                return result["content"][0]["text"] if result["content"] else "操作完成"
            else:
                error_msg = result["content"][0]["text"] if result["content"] else "未知错误"
                return f"❌ 操作失败: {error_msg}"
                
        except Exception as e:
            return f"❌ 执行文件操作时出错: {str(e)}"
    
    async def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        try:
            # 添加用户消息到历史
            self.conversation_history.append({"role": "user", "content": user_message})
            
            # 获取可用的文件操作函数
            functions = self._get_file_operation_functions()
            
            # 调用LLM
            response = self.llm_client.chat(
                messages=self.conversation_history[-10:],  # 保留最近10条消息
                functions=functions if functions else None
            )
            
            message = response.choices[0].message
            
            # 检查是否需要调用工具
            if hasattr(message, 'tool_calls') and message.tool_calls:
                tool_results = []
                
                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    arguments = json.loads(tool_call.function.arguments)
                    
                    # 执行文件操作
                    result = await self._execute_file_operation(function_name, arguments)
                    tool_results.append(result)
                
                # 将工具调用结果添加到对话历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": message.content or "",
                    "tool_calls": [
                        {
                            "id": tc.id,
                            "type": "function",
                            "function": {
                                "name": tc.function.name,
                                "arguments": tc.function.arguments
                            }
                        } for tc in message.tool_calls
                    ]
                })
                
                for i, tool_call in enumerate(message.tool_calls):
                    self.conversation_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_results[i]
                    })
                
                # 再次调用LLM生成最终回复
                final_response = self.llm_client.chat(
                    messages=self.conversation_history[-15:]
                )
                
                final_message = final_response.choices[0].message.content
                self.conversation_history.append({"role": "assistant", "content": final_message})
                
                return final_message
            else:
                # 直接回复
                assistant_message = message.content
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                return assistant_message
                
        except Exception as e:
            error_msg = f"❌ 处理消息时出错: {str(e)}"
            print(error_msg)
            return error_msg
    
    async def cleanup(self):
        """清理资源"""
        if self.mcp_client:
            await self.mcp_client.disconnect()
            print("🔌 MCP连接已断开")
