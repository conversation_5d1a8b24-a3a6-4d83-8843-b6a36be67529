# 🤖 ApplyMCP - 智能文件操作助手

基于MCP (Model Context Protocol) 的智能文件操作、天气查询和时间查询助手。

## ✨ 功能特性

### 📁 文件操作功能
- 📖 读取文件内容
- ✏️ 写入/追加文件内容
- 📁 创建目录
- 🗑️ 删除文件或目录
- 📋 列出目录内容
- 📄 复制文件
- 🔄 移动/重命名文件
- ℹ️ 获取文件信息

### 🌤️ 天气查询功能
- 🌡️ 查询当前天气
- 📊 获取天气预报
- 🌍 支持中英文城市名
- 📈 详细天气信息

### ⏰ 时间查询功能
- 🕐 获取当前时间
- 📅 查询日期信息
- 🌅 时段和问候语
- ⏳ 倒计时计算

## 🏗️ 项目结构

```
ApplyMCP/
├── core/          # 核心模块 (代理、MCP服务器)
├── services/      # 服务模块 (天气、时间服务)
├── clients/       # 客户端模块 (MCP、LLM客户端)
├── config_dir/    # 配置模块 (配置、提示词)
├── tests/         # 测试模块
├── docs/          # 文档
├── examples/      # 示例演示
├── utils/         # 工具模块
└── workspace/     # 工作目录
```

详细结构说明请查看 [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
确保在 `../api.txt` 文件中配置了正确的LLM API密钥。

### 3. 运行程序
```bash
python main.py
```

### 4. 开始对话
```
👤 您: 现在几点了？
🤖 助手: 🕐 当前时间: 2025年08月06日 03:22:35 📅 星期三

👤 您: 查询北京的天气
🤖 助手: 📍 北京 当前天气：🌡️ 温度: 25.3°C...

👤 您: 创建一个文件记录今天的天气
🤖 助手: 已成功创建文件...
```

## 💡 使用示例

### 基本操作
```
# 文件操作
👤 您: 读取 config.py 文件的内容
👤 您: 创建一个名为 notes.txt 的文件
👤 您: 列出当前目录的所有文件

# 天气查询
👤 您: 上海的天气怎么样？
👤 您: 深圳未来3天的天气预报

# 时间查询
👤 您: 今天是星期几？
👤 您: 距离春节还有多少天？
```

### 高级操作
```
# 组合操作
👤 您: 把当前时间和北京天气信息保存到 daily_info.txt 文件中
👤 您: 整理项目文件，把所有Python文件放到src目录
```

## 🔧 配置说明

### 主要配置项 (config_dir/config.py)
- `LLM_BASE_URL`: LLM API基础URL
- `LLM_MODEL`: 使用的LLM模型
- `WEATHER_API_KEY`: 天气API密钥
- `DEFAULT_WORK_DIR`: 默认工作目录
- `SAFE_MODE`: 安全模式开关

### 安全设置
- 文件大小限制: 10MB
- 支持的文件类型: .txt, .py, .json, .md 等
- 路径安全检查
- 操作权限控制

## 🧪 测试

### 运行测试
```bash
# 安全测试
python -m tests.security_test

# 功能演示
python -m examples.demo
```

## 📚 文档

- [详细使用说明](docs/使用说明.md)
- [安全配置说明](docs/安全配置说明.md)
- [项目结构说明](PROJECT_STRUCTURE.md)

## 🛠️ 技术架构

### MCP架构
1. **MCP服务器**: 提供工具接口 (core/file_operations_mcp.py)
2. **MCP客户端**: 与服务器通信 (clients/mcp_client.py)
3. **对话代理**: 集成LLM和MCP (core/agent.py)
4. **主程序**: 用户交互界面 (main.py)

### 模块化设计
- 核心功能模块化
- 服务独立封装
- 配置统一管理
- 测试完整覆盖

## 🎯 扩展开发

### 添加新服务
1. 在 `services/` 目录创建服务文件
2. 在MCP服务器中注册工具
3. 在代理中添加函数定义
4. 更新配置和文档

### 自定义配置
修改 `config_dir/config.py` 中的配置项来适应需求。

## 🔍 故障排除

### 常见问题
1. **MCP服务启动失败**: 检查Python版本和依赖包
2. **LLM调用失败**: 检查API密钥和网络连接
3. **文件操作权限错误**: 检查路径权限和安全设置

### 调试模式
```bash
python main_debug.py
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
