# 文件操作MCP安全配置说明

## 🔒 安全限制概述

为了确保在测试阶段的安全性，ApplyMCP项目实现了严格的文件操作安全限制，防止误操作重要文件或访问系统敏感区域。

## 🛡️ 安全特性

### 1. 目录访问限制
- **允许范围**: 仅限ApplyMCP项目目录内 (`D:\BaiduNetdiskDownload\SOVITS\LLM对话\dev\ApplyMCP`)
- **禁止访问**: 
  - 父目录及其他外部目录
  - 系统目录 (如 `C:\Windows`, `/etc`, `/usr` 等)
  - 通过相对路径 (`../`, `../../` 等) 访问外部

### 2. 核心文件保护
以下核心文件被保护，禁止修改或删除：
- `config.py` - 配置文件
- `file_operations_mcp.py` - MCP服务器
- `agent.py` - 核心代理
- `llm_client.py` - LLM客户端
- `mcp_client.py` - MCP客户端

### 3. 系统目录保护
禁止访问以下系统目录：
- `__pycache__` - Python缓存目录
- `.git` - Git版本控制目录
- `.vscode` - VS Code配置目录
- `node_modules` - Node.js模块目录

## ⚙️ 配置详情

### 安全配置项 (config.py)

```python
# 安全配置
SAFE_MODE = True  # 启用安全模式
ALLOWED_BASE_DIR = Path(__file__).parent.resolve()  # 允许的基础目录

# 禁止操作的目录
FORBIDDEN_DIRS = [
    "__pycache__",
    ".git", 
    ".vscode",
    "node_modules"
]

# 禁止操作的文件
FORBIDDEN_FILES = [
    "config.py",
    "file_operations_mcp.py", 
    "agent.py",
    "llm_client.py",
    "mcp_client.py"
]
```

### 安全检查函数

```python
@classmethod
def is_path_safe(cls, file_path: str) -> tuple[bool, str]:
    """检查路径是否安全"""
    # 1. 检查是否在允许的基础目录内
    # 2. 检查是否包含禁止的目录
    # 3. 检查是否是禁止的文件
```

## 🧪 安全测试

### 测试覆盖范围
1. ✅ **允许的操作**
   - 在项目目录内创建文件
   - 在项目目录内创建子目录
   - 读取项目目录内的文件
   - 修改非核心文件

2. ❌ **被阻止的操作**
   - 访问父目录文件
   - 在父目录创建文件
   - 访问系统目录
   - 修改核心文件
   - 删除核心文件
   - 访问系统敏感目录
   - 通过相对路径访问外部

### 运行安全测试

```bash
python security_test.py
```

### 测试结果示例
```
🧪 执行 10 个安全测试用例...
✅ 通过: 10
❌ 失败: 0  
📈 成功率: 100.0%
🎉 所有安全测试通过！系统安全限制工作正常。
```

## 🔧 实现机制

### 1. 路径解析和验证
- 将所有路径转换为绝对路径
- 使用 `Path.resolve()` 解析符号链接和相对路径
- 检查解析后的路径是否在允许范围内

### 2. 多层安全检查
- **MCP服务器层**: 在每个文件操作函数中进行安全检查
- **配置层**: 集中的安全策略配置
- **客户端层**: 可选的客户端侧验证

### 3. 错误处理
- 安全违规时返回明确的错误信息
- 不暴露系统敏感信息
- 记录安全事件（可扩展）

## 🚨 安全警告

### 当前限制
- 安全检查基于路径字符串匹配
- 不防护符号链接攻击（已通过resolve()缓解）
- 不限制文件大小和数量

### 生产环境建议
1. **启用更严格的权限控制**
2. **添加操作日志记录**
3. **实现文件大小和数量限制**
4. **添加用户身份验证**
5. **使用沙箱环境**

## 📝 自定义安全配置

### 修改允许的目录
```python
# 在config.py中修改
ALLOWED_BASE_DIR = Path("/your/safe/directory").resolve()
```

### 添加禁止的文件类型
```python
# 添加到FORBIDDEN_FILES列表
FORBIDDEN_FILES.extend([
    "sensitive_config.json",
    "private_key.pem"
])
```

### 临时禁用安全模式
```python
# 仅用于调试，生产环境不建议
SAFE_MODE = False
```

## 🔍 故障排除

### 常见问题

1. **合法操作被阻止**
   - 检查文件路径是否在允许范围内
   - 确认文件名不在禁止列表中

2. **安全检查失效**
   - 验证 `SAFE_MODE = True`
   - 检查路径解析是否正确

3. **测试失败**
   - 运行 `python security_test.py` 诊断
   - 检查文件权限和目录结构

### 调试模式
```python
# 在安全检查函数中添加调试信息
print(f"检查路径: {file_path}")
print(f"解析后路径: {abs_path}")
print(f"允许的基础目录: {cls.ALLOWED_BASE_DIR}")
```

## 📊 安全状态

当前安全配置状态：
- ✅ 安全模式已启用
- ✅ 目录访问限制已配置
- ✅ 核心文件保护已启用
- ✅ 系统目录保护已启用
- ✅ 安全测试100%通过

## 🎯 总结

ApplyMCP项目的安全限制确保了：
1. **文件操作仅限于项目目录内**
2. **核心文件得到保护**
3. **系统敏感区域无法访问**
4. **测试阶段的安全性**

这些安全措施为项目的安全测试和开发提供了可靠的保障。
