#!/usr/bin/env python3
"""
快速验证安全功能
"""

import asyncio
from mcp_client import MCPClient
import os

async def quick_verify():
    """快速验证安全功能"""
    print("🔍 快速验证安全功能...")
    
    script_path = os.path.join(os.path.dirname(__file__), "file_operations_mcp.py")
    client = MCPClient(script_path)
    
    try:
        await client.connect()
        print("✅ MCP连接成功")
        
        # 测试1: 正常操作
        result1 = await client.call_tool("write_file", {
            "file_path": "test_security.txt", 
            "content": "安全测试"
        })
        print(f"正常操作: {'✅ 成功' if result1['success'] else '❌ 失败'}")
        
        # 测试2: 危险操作 - 访问父目录
        result2 = await client.call_tool("read_file", {
            "file_path": "../api.txt"
        })
        is_blocked = not result2['success'] and "安全限制" in str(result2)
        print(f"访问父目录: {'✅ 被阻止' if is_blocked else '❌ 未阻止'}")
        
        # 测试3: 危险操作 - 修改核心文件
        result3 = await client.call_tool("write_file", {
            "file_path": "config.py",
            "content": "# 恶意修改"
        })
        is_blocked = not result3['success'] and "安全限制" in str(result3)
        print(f"修改核心文件: {'✅ 被阻止' if is_blocked else '❌ 未阻止'}")
        
        # 清理
        await client.call_tool("delete_file", {"file_path": "test_security.txt"})
        
        await client.disconnect()
        print("🔒 安全验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(quick_verify())
