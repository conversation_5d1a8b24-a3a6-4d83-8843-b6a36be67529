#!/usr/bin/env python3
"""
安全限制测试脚本
验证文件操作的安全限制功能
"""

import asyncio
import os
from pathlib import Path
from mcp_client import MCPClient

async def test_security_restrictions():
    """测试安全限制功能"""
    print("🔒 开始安全限制测试...")
    print("=" * 50)
    
    # 创建MCP客户端
    script_path = os.path.join(os.path.dirname(__file__), "file_operations_mcp.py")
    client = MCPClient(script_path)
    
    try:
        # 连接到MCP服务器
        print("🔌 连接MCP服务器...")
        if not await client.connect():
            print("❌ 连接失败")
            return False
        
        print("✅ 连接成功")
        
        # 获取当前ApplyMCP目录的绝对路径
        current_dir = Path(__file__).parent.resolve()
        parent_dir = current_dir.parent
        
        print(f"📁 当前允许的目录: {current_dir}")
        print(f"🚫 父目录（应被禁止）: {parent_dir}")
        
        # 测试用例
        test_cases = [
            {
                "name": "尝试在允许目录内创建文件",
                "operation": "write_file",
                "args": {"file_path": "test_safe.txt", "content": "这是安全的测试"},
                "should_succeed": True
            },
            {
                "name": "尝试访问父目录文件",
                "operation": "read_file", 
                "args": {"file_path": "../api.txt"},
                "should_succeed": False
            },
            {
                "name": "尝试在父目录创建文件",
                "operation": "write_file",
                "args": {"file_path": "../dangerous_file.txt", "content": "危险操作"},
                "should_succeed": False
            },
            {
                "name": "尝试访问系统目录",
                "operation": "list_directory",
                "args": {"dir_path": "C:\\Windows"},
                "should_succeed": False
            },
            {
                "name": "尝试修改核心配置文件",
                "operation": "write_file",
                "args": {"file_path": "config.py", "content": "# 恶意修改"},
                "should_succeed": False
            },
            {
                "name": "尝试删除核心文件",
                "operation": "delete_file",
                "args": {"file_path": "agent.py"},
                "should_succeed": False
            },
            {
                "name": "尝试访问__pycache__目录",
                "operation": "list_directory",
                "args": {"dir_path": "__pycache__"},
                "should_succeed": False
            },
            {
                "name": "在允许目录内创建子目录",
                "operation": "create_directory",
                "args": {"dir_path": "safe_test_dir"},
                "should_succeed": True
            },
            {
                "name": "在允许的子目录内操作",
                "operation": "write_file",
                "args": {"file_path": "safe_test_dir/test.txt", "content": "子目录测试"},
                "should_succeed": True
            },
            {
                "name": "尝试通过相对路径访问外部",
                "operation": "read_file",
                "args": {"file_path": "../../sensitive_file.txt"},
                "should_succeed": False
            }
        ]
        
        print(f"\n🧪 执行 {len(test_cases)} 个安全测试用例...")
        print("=" * 50)
        
        passed_tests = 0
        failed_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试 {i}: {test_case['name']}")
            print(f"🔧 操作: {test_case['operation']}")
            print(f"📝 参数: {test_case['args']}")
            print(f"🎯 预期: {'应该成功' if test_case['should_succeed'] else '应该被阻止'}")
            
            try:
                result = await client.call_tool(test_case['operation'], test_case['args'])
                success = result.get('success', False)
                content = result.get('content', [])
                message = content[0].get('text', '') if content else ''
                
                print(f"📤 结果: {'成功' if success else '失败'}")
                if message:
                    print(f"💬 消息: {message[:100]}...")
                
                # 验证结果是否符合预期
                if test_case['should_succeed']:
                    if success:
                        print("✅ 测试通过 - 允许的操作成功执行")
                        passed_tests += 1
                    else:
                        print("❌ 测试失败 - 允许的操作被错误阻止")
                        failed_tests += 1
                else:
                    if not success and "安全限制" in message:
                        print("✅ 测试通过 - 危险操作被正确阻止")
                        passed_tests += 1
                    elif not success:
                        print("⚠️ 测试部分通过 - 操作被阻止但不是因为安全限制")
                        passed_tests += 1
                    else:
                        print("❌ 测试失败 - 危险操作未被阻止！")
                        failed_tests += 1
                
            except Exception as e:
                print(f"❌ 测试异常: {str(e)}")
                failed_tests += 1
            
            print("-" * 50)
        
        # 清理测试文件
        print("\n🧹 清理测试文件...")
        try:
            await client.call_tool("delete_file", {"file_path": "test_safe.txt"})
            await client.call_tool("delete_file", {"file_path": "safe_test_dir"})
            print("✅ 清理完成")
        except:
            print("⚠️ 清理时出现问题（可能文件不存在）")
        
        # 测试总结
        print(f"\n📊 测试总结:")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📈 成功率: {passed_tests/(passed_tests+failed_tests)*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 所有安全测试通过！系统安全限制工作正常。")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查安全配置。")
        
        return failed_tests == 0
        
    except Exception as e:
        print(f"❌ 安全测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 断开连接
        await client.disconnect()
        print("🔌 MCP连接已断开")

async def main():
    """主函数"""
    print("🛡️ 文件操作安全限制测试")
    print("=" * 50)
    print("本测试将验证以下安全特性：")
    print("1. 限制文件操作仅在ApplyMCP目录内")
    print("2. 禁止访问父目录和系统目录")
    print("3. 保护核心文件不被修改")
    print("4. 阻止访问系统敏感目录")
    print("=" * 50)
    
    success = await test_security_restrictions()
    
    if success:
        print("\n🔒 安全限制功能正常工作，系统安全！")
    else:
        print("\n⚠️ 发现安全问题，请检查配置！")

if __name__ == "__main__":
    asyncio.run(main())
