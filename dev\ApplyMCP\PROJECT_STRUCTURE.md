# ApplyMCP 项目结构说明

## 📁 目录结构

```
ApplyMCP/
├── 📁 core/                    # 核心模块
│   ├── __init__.py
│   ├── agent.py               # 主要的对话代理
│   └── file_operations_mcp.py # MCP服务器实现
│
├── 📁 services/               # 服务模块
│   ├── __init__.py
│   ├── weather_service.py     # 天气查询服务
│   └── time_service.py        # 时间查询服务
│
├── 📁 clients/                # 客户端模块
│   ├── __init__.py
│   ├── mcp_client.py          # MCP客户端
│   └── llm_client.py          # LLM客户端
│
├── 📁 config_dir/             # 配置模块
│   ├── __init__.py
│   ├── config.py              # 项目配置
│   └── prompts.py             # 提示词配置
│
├── 📁 tests/                  # 测试模块
│   ├── __init__.py
│   ├── security_test.py       # 安全测试
│   └── security_dialog_test.py # 安全对话测试
│
├── 📁 docs/                   # 文档
│   ├── README.md              # 项目说明
│   ├── 使用说明.md            # 使用指南
│   └── 安全配置说明.md        # 安全配置说明
│
├── 📁 examples/               # 示例和演示
│   ├── __init__.py
│   └── demo.py                # 演示脚本
│
├── 📁 utils/                  # 工具模块
│   ├── __init__.py
│   └── verify_security.py     # 安全验证工具
│
├── 📁 workspace/              # 工作目录
│   └── (用户文件操作的默认目录)
│
├── 📁 __pycache__/            # Python缓存文件
├── 📁 notes/                  # 笔记目录
├── 📁 src/                    # 其他源码
├── 📁 test_dir/               # 测试目录
│
├── main.py                    # 主程序入口
├── main_debug.py              # 调试版主程序
├── run.py                     # 运行脚本
├── requirements.txt           # 依赖包列表
└── project_readme.md          # 项目说明
```

## 🔧 模块说明

### Core 核心模块
- **agent.py**: 主要的对话代理，集成LLM和MCP功能
- **file_operations_mcp.py**: MCP服务器实现，提供文件操作、天气查询、时间查询等工具

### Services 服务模块
- **weather_service.py**: 天气查询服务，支持当前天气和天气预报
- **time_service.py**: 时间查询服务，提供时间、日期、倒计时等功能

### Clients 客户端模块
- **mcp_client.py**: MCP客户端，与MCP服务器通信
- **llm_client.py**: LLM客户端，处理大语言模型调用

### Config 配置模块
- **config.py**: 项目配置，包含API密钥、路径设置等
- **prompts.py**: 提示词配置，包含系统提示和操作提示

### Tests 测试模块
- **security_test.py**: 安全功能测试
- **security_dialog_test.py**: 安全对话测试

### Docs 文档模块
- **README.md**: 详细的项目说明文档
- **使用说明.md**: 用户使用指南
- **安全配置说明.md**: 安全配置详细说明

### Examples 示例模块
- **demo.py**: 功能演示脚本

### Utils 工具模块
- **verify_security.py**: 安全验证相关工具

## 🚀 使用方式

### 启动主程序
```bash
python main.py
```

### 运行测试
```bash
python -m tests.security_test
```

### 运行演示
```bash
python -m examples.demo
```

## 📦 导入方式

由于采用了模块化结构，导入时需要使用相对路径：

```python
# 导入核心模块
from core.agent import FileOperationAgent

# 导入服务模块
from services.weather_service import WeatherService
from services.time_service import TimeService

# 导入客户端模块
from clients.mcp_client import MCPClient
from clients.llm_client import LLMClient

# 导入配置
from config_dir.config import Config
from config_dir.prompts import SYSTEM_PROMPT
```

## 🔄 扩展指南

### 添加新服务
1. 在 `services/` 目录下创建新的服务文件
2. 在 `services/__init__.py` 中添加导入
3. 在 `core/file_operations_mcp.py` 中注册新工具
4. 在 `core/agent.py` 中添加函数定义

### 添加新测试
1. 在 `tests/` 目录下创建测试文件
2. 使用标准的Python测试框架

### 添加新文档
1. 在 `docs/` 目录下创建文档文件
2. 使用Markdown格式编写

这种模块化结构使项目更加清晰、易于维护和扩展。
