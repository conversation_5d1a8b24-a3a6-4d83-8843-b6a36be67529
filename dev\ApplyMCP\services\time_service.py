#!/usr/bin/env python3
"""
时间服务模块
提供系统时间查询功能
"""

import datetime
import calendar
from typing import Dict, Any

class TimeService:
    def __init__(self):
        pass
        
    def get_current_time(self, format_type: str = "detailed") -> Dict[str, Any]:
        """获取当前时间"""
        now = datetime.datetime.now()
        
        if format_type == "simple":
            return {
                "time": now.strftime("%H:%M:%S"),
                "date": now.strftime("%Y-%m-%d")
            }
        elif format_type == "detailed":
            return {
                "datetime": now.strftime("%Y年%m月%d日 %H:%M:%S"),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "hour": now.hour,
                "minute": now.minute,
                "second": now.second,
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            }
        else:  # timestamp
            return {
                "timestamp": now.timestamp(),
                "iso_format": now.isoformat(),
                "formatted": now.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def get_time_info(self, info_type: str = "period") -> Dict[str, Any]:
        """获取时间相关信息"""
        now = datetime.datetime.now()
        
        if info_type == "weekday":
            return {
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
                "is_weekend": now.weekday() >= 5
            }
        elif info_type == "season":
            month = now.month
            if month in [12, 1, 2]:
                season = "冬季"
            elif month in [3, 4, 5]:
                season = "春季"
            elif month in [6, 7, 8]:
                season = "夏季"
            else:
                season = "秋季"
            return {"season": season, "month": month}
        else:  # period
            hour = now.hour
            if 5 <= hour < 12:
                period = "上午"
                greeting = "早上好"
            elif 12 <= hour < 18:
                period = "下午"
                greeting = "下午好"
            elif 18 <= hour < 22:
                period = "傍晚"
                greeting = "晚上好"
            else:
                period = "深夜"
                greeting = "夜深了"
            
            return {
                "period": period,
                "greeting": greeting,
                "hour": hour,
                "is_work_time": 9 <= hour < 18,
                "is_sleep_time": hour >= 23 or hour < 6
            }

    def get_date_info(self, info_type: str = "basic") -> Dict[str, Any]:
        """获取日期相关信息"""
        now = datetime.datetime.now()
        
        if info_type == "basic":
            return {
                "date": now.strftime("%Y年%m月%d日"),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            }
        elif info_type == "lunar":
            # 简单的农历信息（这里只是示例，实际需要农历转换库）
            return {
                "solar_date": now.strftime("%Y年%m月%d日"),
                "note": "农历信息需要专门的农历转换库支持"
            }
        else:  # detailed
            # 计算一年中的第几天
            day_of_year = now.timetuple().tm_yday
            # 计算一年中的第几周
            week_of_year = now.isocalendar()[1]
            
            return {
                "date": now.strftime("%Y年%m月%d日"),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
                "day_of_year": day_of_year,
                "week_of_year": week_of_year,
                "is_leap_year": calendar.isleap(now.year),
                "days_in_month": calendar.monthrange(now.year, now.month)[1]
            }

    def format_time_response(self, time_data: Dict[str, Any], response_type: str = "current") -> str:
        """格式化时间信息为可读文本"""
        if response_type == "current":
            if "datetime" in time_data:
                result = f"🕐 当前时间: {time_data['datetime']}\n"
                result += f"📅 {time_data['weekday_cn']}"
            else:
                result = f"🕐 当前时间: {time_data['time']}\n"
                result += f"📅 日期: {time_data['date']}"
        elif response_type == "period":
            result = f"🌅 {time_data['greeting']}！\n"
            result += f"⏰ 现在是{time_data['period']} ({time_data['hour']}点)\n"
            if time_data['is_work_time']:
                result += "💼 正值工作时间"
            elif time_data['is_sleep_time']:
                result += "😴 该休息了"
            else:
                result += "🎯 自由时间"
        elif response_type == "weekday":
            result = f"📅 今天是{time_data['weekday_cn']}\n"
            if time_data['is_weekend']:
                result += "🎉 周末愉快！"
            else:
                result += "💪 工作日加油！"
        elif response_type == "season":
            result = f"🌸 当前季节: {time_data['season']}\n"
            result += f"📅 {time_data['month']}月"
        else:  # detailed date
            result = f"📅 {time_data['date']} {time_data['weekday_cn']}\n"
            result += f"📊 今年第{time_data['day_of_year']}天，第{time_data['week_of_year']}周\n"
            result += f"📆 本月共{time_data['days_in_month']}天\n"
            if time_data['is_leap_year']:
                result += "🗓️ 今年是闰年"
            else:
                result += "🗓️ 今年是平年"
                
        return result

    def get_countdown(self, target_date: str) -> Dict[str, Any]:
        """计算到目标日期的倒计时"""
        try:
            now = datetime.datetime.now()
            target = datetime.datetime.strptime(target_date, "%Y-%m-%d")
            
            if target.date() < now.date():
                return {
                    "error": "目标日期已过去",
                    "target_date": target_date
                }
            
            delta = target - now
            days = delta.days
            hours, remainder = divmod(delta.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            return {
                "target_date": target_date,
                "days": days,
                "hours": hours,
                "minutes": minutes,
                "total_days": days,
                "formatted": f"{days}天{hours}小时{minutes}分钟"
            }
            
        except ValueError:
            return {
                "error": "日期格式错误，请使用 YYYY-MM-DD 格式",
                "example": "2024-12-31"
            }
