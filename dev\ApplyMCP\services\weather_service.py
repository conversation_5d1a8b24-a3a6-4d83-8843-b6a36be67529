#!/usr/bin/env python3
"""
天气服务模块
提供天气查询功能
"""

import requests
from typing import Dict, Any

class WeatherService:
    def __init__(self, api_key: str, base_url: str = "https://api.openweathermap.org/data/2.5"):
        self.api_key = api_key
        self.base_url = base_url
        # 中文城市名到英文的映射
        self.city_mapping = {
            "北京": "Beijing",
            "上海": "Shanghai", 
            "广州": "Guangzhou",
            "深圳": "Shenzhen",
            "杭州": "Hangzhou",
            "南京": "Nanjing",
            "武汉": "Wuhan",
            "成都": "Cheng<PERSON>",
            "重庆": "Chongqing",
            "天津": "Tianjin",
            "西安": "Xi'an",
            "苏州": "Suzhou",
            "长沙": "Changsha",
            "沈阳": "Shenyang",
            "青岛": "Qingdao",
            "郑州": "Zhengzhou",
            "大连": "<PERSON><PERSON>",
            "东莞": "Dongguan",
            "宁波": "Ningbo",
            "厦门": "Xiamen",
            "福州": "Fuzhou",
            "无锡": "Wuxi",
            "合肥": "<PERSON><PERSON><PERSON>",
            "昆明": "Kun<PERSON>",
            "哈尔滨": "Harbin",
            "济南": "Jinan",
            "佛山": "<PERSON>oshan",
            "长春": "Changchun",
            "温州": "Wenzhou",
            "石家庄": "Shijiazhuang",
            "南宁": "Nanning",
            "常州": "Changzhou",
            "泉州": "Quanzhou",
            "南昌": "Nanchang",
            "贵阳": "Guiyang",
            "太原": "Taiyuan",
            "烟台": "Yantai",
            "嘉兴": "Jiaxing",
            "南通": "Nantong",
            "金华": "Jinhua",
            "珠海": "Zhuhai",
            "惠州": "Huizhou",
            "徐州": "Xuzhou",
            "海口": "Haikou",
            "乌鲁木齐": "Urumqi",
            "绍兴": "Shaoxing",
            "中山": "Zhongshan",
            "台州": "Taizhou",
            "兰州": "Lanzhou"
        }

    def _translate_city_name(self, city: str) -> str:
        """将中文城市名翻译为英文，如果已经是英文则直接返回"""
        return self.city_mapping.get(city, city)
        
    def get_current_weather(self, city: str) -> Dict[str, Any]:
        """获取当前天气"""
        english_city = self._translate_city_name(city)
        url = f"{self.base_url}/weather"
        params = {
            "q": english_city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn"
        }

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # 格式化返回数据
            weather_info = {
                "city": data.get("name", city),
                "country": data.get("sys", {}).get("country", ""),
                "temperature": data.get("main", {}).get("temp", 0),
                "feels_like": data.get("main", {}).get("feels_like", 0),
                "humidity": data.get("main", {}).get("humidity", 0),
                "pressure": data.get("main", {}).get("pressure", 0),
                "description": data.get("weather", [{}])[0].get("description", ""),
                "wind_speed": data.get("wind", {}).get("speed", 0),
                "visibility": data.get("visibility", 0) / 1000 if data.get("visibility") else 0,
                "clouds": data.get("clouds", {}).get("all", 0)
            }
            
            return weather_info
            
        except requests.RequestException as e:
            raise Exception(f"天气查询失败: {str(e)}")
    
    def get_weather_forecast(self, city: str, days: int = 3) -> Dict[str, Any]:
        """获取天气预报"""
        english_city = self._translate_city_name(city)
        url = f"{self.base_url}/forecast"
        params = {
            "q": english_city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn",
            "cnt": days * 8  # 每天8个时间点（3小时间隔）
        }

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # 格式化预报数据
            forecasts = []
            for item in data.get("list", []):
                forecast = {
                    "datetime": item.get("dt_txt", ""),
                    "temperature": item.get("main", {}).get("temp", 0),
                    "feels_like": item.get("main", {}).get("feels_like", 0),
                    "humidity": item.get("main", {}).get("humidity", 0),
                    "description": item.get("weather", [{}])[0].get("description", ""),
                    "wind_speed": item.get("wind", {}).get("speed", 0),
                    "clouds": item.get("clouds", {}).get("all", 0)
                }
                forecasts.append(forecast)
            
            return {
                "city": data.get("city", {}).get("name", city),
                "country": data.get("city", {}).get("country", ""),
                "forecasts": forecasts
            }
            
        except requests.RequestException as e:
            raise Exception(f"天气预报查询失败: {str(e)}")

    def format_weather_response(self, weather_data: Dict[str, Any]) -> str:
        """格式化天气信息为可读文本"""
        if "forecasts" in weather_data:
            # 天气预报格式化
            result = f"📍 {weather_data['city']} 天气预报：\n\n"
            for forecast in weather_data["forecasts"][:8]:  # 显示前8个时间点
                result += f"🕐 {forecast['datetime']}\n"
                result += f"🌡️ 温度: {forecast['temperature']:.1f}°C (体感 {forecast['feels_like']:.1f}°C)\n"
                result += f"☁️ 天气: {forecast['description']}\n"
                result += f"💨 风速: {forecast['wind_speed']:.1f} m/s\n"
                result += f"💧 湿度: {forecast['humidity']}%\n\n"
        else:
            # 当前天气格式化
            result = f"📍 {weather_data['city']} 当前天气：\n\n"
            result += f"🌡️ 温度: {weather_data['temperature']:.1f}°C (体感 {weather_data['feels_like']:.1f}°C)\n"
            result += f"☁️ 天气: {weather_data['description']}\n"
            result += f"💨 风速: {weather_data['wind_speed']:.1f} m/s\n"
            result += f"💧 湿度: {weather_data['humidity']}%\n"
            result += f"🌫️ 能见度: {weather_data['visibility']:.1f} km\n"
            result += f"☁️ 云量: {weather_data['clouds']}%\n"
            result += f"🌊 气压: {weather_data['pressure']} hPa"
            
        return result
